import {OAuthStrategy} from '@feathersjs/authentication-oauth';
import {handleRedirect} from './utils.js';
import {_get} from '../../../utils/dash-utils.js';
import {Application} from '../../../declarations.js';
import {AuthenticationParams, AuthenticationRequest} from '@feathersjs/authentication';

export class GoogleStrategy extends OAuthStrategy {
    app?: Application = undefined

    constructor(app) {
        super();
        this.app = app;
    }

    async getRedirect(data, params) {
        return handleRedirect(data, params);
    }

    async getEntityData(profile) {

        // this will set 'googleId'
        const baseData = await super.getEntityData(profile, null, {});

        // this will grab the picture and email address of the Google profile
        return {
            ...baseData,
            email: profile.email,
            name: profile.name
        };
    }

    //@ts-ignore
    async authenticate(authentication: AuthenticationRequest, originalParams: AuthenticationParams) {
        const entity: string = this.configuration.entity
        const { provider, ...params } = originalParams
        const profile = await this.getProfile(authentication, params)
        const existingEntity = (await this.findEntity(profile, params)) || (await this.getCurrentEntity(params))

        params.admin_pass = true;
        const authEntity = !existingEntity
            ? await this.createEntity(profile, params)
            : await this.updateEntity(existingEntity, profile, params)

        return {
            authentication: { strategy: this.name },
            [entity]: await this.getEntity(authEntity, originalParams)
        }
    }

    async findEntity(profile, params) {
        const query: { did?: string, email?: string, _id?: any } = {};
        if (params.query.loginId) query._id = params.query.loginId
        else if (params.query.ucan_aud) query.did = params.query.ucan_aud;
        else query.email = profile.email;
        params.loginOptions ? params.loginOptions.existCheck = true : params.loginOptions = { existCheck: true };
        const existing = await this.app?.service('logins').find({
            ...params,
            query,
        });
        let entity = _get(existing, 'data[0]');
        if(!existing?.total){
            const e = await (this.app as any)?.service('logins').create({email: profile.email, name: profile.name, [`${this.name}Id`]: profile.sub || profile.id , isVerified: true })
            entity = e;
        }
        params.login = entity;
        return entity
    }

    async updateEntity(entity, profile, params) {
        let pChange = false;
        const changeEntity: typeof entity = { googleId: profile.sub || profile.id }
        let person: { [key: string]: any } = {_id: _get(entity, '_fastjoin.owner._id')};
        if (!entity?.isVerified) {
            changeEntity.isVerified = true;
        }
        if (!entity?.email) {
            pChange = true;
            changeEntity.email = profile.email;
            person.email = profile.email;
        }
        if (!person.name) {
            pChange = true;
            person.name = profile.name;
        }
        if (!_get(entity, 'avatar.url') && profile.picture && !person.avatar) {
            pChange = true;
            person.avatar = {url: profile.picture};
        }
        if (pChange) {
            let call = 'create';
            const args = [person];
            if (person._id) {
                call = 'patch';
                args.unshift(person._id);
            } else delete person._id;
            const p = await this.app?.service('ppls')[call](...args)
                .catch(err => {
                    console.log(err);
                })
            if (!entity.owner) {
                changeEntity.owner = p?._id;
            }
        }
        const res = await this.app?.service('logins')._patch(entity._id, changeEntity, { ...params, skip_hooks: true, admin_pass: true, query: undefined }) || entity;
        return res;
    }
}
