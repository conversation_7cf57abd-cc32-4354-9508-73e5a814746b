// TypeBox schema for gps service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const gpsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  org: Type.Optional(ObjectIdSchema()),
  plan: Type.Optional(ObjectIdSchema()),
  runRequest: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Gps = Static<typeof gpsSchema>
export const gpsValidator = getValidator(gpsSchema, dataValidator)
export const gpsResolver = resolve<Gps, HookContext>({})
export const gpsExternalResolver = resolve<Gps, HookContext>({})

export const gpsDataSchema = Type.Object({
  ...Type.Omit(gpsSchema, ['_id']).properties
}, { additionalProperties: false })

export type GpsData = Static<typeof gpsDataSchema>
export const gpsDataValidator = getValidator(gpsDataSchema, dataValidator)
export const gpsDataResolver = resolve<GpsData, HookContext>({})

export const gpsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(gpsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
}, { additionalProperties: false })
export type GpsPatch = Static<typeof gpsPatchSchema>
export const gpsPatchValidator = getValidator(gpsPatchSchema, dataValidator)
export const gpsPatchResolver = resolve<GpsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const gpsQueryProperties = Type.Object({
  ...Type.Pick(gpsSchema, ['_id', 'org', 'plan', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const gpsQuerySchema = queryWrapper(gpsQueryProperties)
export type GpsQuery = Static<typeof gpsQuerySchema>
export const gpsQueryValidator = getValidator(gpsQuerySchema, queryValidator)
export const gpsQueryResolver = resolve<GpsQuery, HookContext>({})
