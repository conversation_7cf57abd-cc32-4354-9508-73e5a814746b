// TypeBox schema for health-shares service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const healthSharesSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
}, { additionalProperties: false })

export type HealthShares = Static<typeof healthSharesSchema>
export const healthSharesValidator = getValidator(healthSharesSchema, dataValidator)
export const healthSharesResolver = resolve<HealthShares, HookContext>({})
export const healthSharesExternalResolver = resolve<HealthShares, HookContext>({})

export const healthSharesDataSchema = Type.Object({
  ...Type.Omit(healthSharesSchema, ['_id']).properties
}, { additionalProperties: false })

export type HealthSharesData = Static<typeof healthSharesDataSchema>
export const healthSharesDataValidator = getValidator(healthSharesDataSchema, dataValidator)
export const healthSharesDataResolver = resolve<HealthSharesData, HookContext>({})

export const healthSharesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(healthSharesSchema, ['_id'])).properties
}, { additionalProperties: false })
export type HealthSharesPatch = Static<typeof healthSharesPatchSchema>
export const healthSharesPatchValidator = getValidator(healthSharesPatchSchema, dataValidator)
export const healthSharesPatchResolver = resolve<HealthSharesPatch, HookContext>({})

// Allow querying on any field from the main schema
const healthSharesQueryProperties = healthSharesSchema
export const healthSharesQuerySchema = queryWrapper(healthSharesQueryProperties)
export type HealthSharesQuery = Static<typeof healthSharesQuerySchema>
export const healthSharesQueryValidator = getValidator(healthSharesQuerySchema, queryValidator)
export const healthSharesQueryResolver = resolve<HealthSharesQuery, HookContext>({})
