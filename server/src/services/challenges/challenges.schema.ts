// TypeBox schema for challenges service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const challengesSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  kind: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Challenges = Static<typeof challengesSchema>
export const challengesValidator = getValidator(challengesSchema, dataValidator)
export const challengesResolver = resolve<Challenges, HookContext>({})
export const challengesExternalResolver = resolve<Challenges, HookContext>({})

export const challengesDataSchema = Type.Object({
  ...Type.Omit(challengesSchema, ['_id']).properties
}, { additionalProperties: false })

export type ChallengesData = Static<typeof challengesDataSchema>
export const challengesDataValidator = getValidator(challengesDataSchema, dataValidator)
export const challengesDataResolver = resolve<ChallengesData, HookContext>({})

export const challengesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(challengesSchema, ['_id'])).properties
}, { additionalProperties: false })
export type ChallengesPatch = Static<typeof challengesPatchSchema>
export const challengesPatchValidator = getValidator(challengesPatchSchema, dataValidator)
export const challengesPatchResolver = resolve<ChallengesPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const challengesQueryProperties = Type.Object({
  ...Type.Pick(challengesSchema, ['_id', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const challengesQuerySchema = queryWrapper(challengesQueryProperties)
export type ChallengesQuery = Static<typeof challengesQuerySchema>
export const challengesQueryValidator = getValidator(challengesQuerySchema, queryValidator)
export const challengesQueryResolver = resolve<ChallengesQuery, HookContext>({})
