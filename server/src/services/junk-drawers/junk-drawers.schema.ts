// TypeBox schema for junk-drawers service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const junkDrawersSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  drawer: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type JunkDrawers = Static<typeof junkDrawersSchema>
export const junkDrawersValidator = getValidator(junkDrawersSchema, dataValidator)
export const junkDrawersResolver = resolve<JunkDrawers, HookContext>({})
export const junkDrawersExternalResolver = resolve<JunkDrawers, HookContext>({})

export const junkDrawersDataSchema = Type.Object({
  ...Type.Omit(junkDrawersSchema, ['_id']).properties
}, { additionalProperties: false })

export type JunkDrawersData = Static<typeof junkDrawersDataSchema>
export const junkDrawersDataValidator = getValidator(junkDrawersDataSchema, dataValidator)
export const junkDrawersDataResolver = resolve<JunkDrawersData, HookContext>({})

export const junkDrawersPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(junkDrawersSchema, ['_id'])).properties
}, { additionalProperties: false })
export type JunkDrawersPatch = Static<typeof junkDrawersPatchSchema>
export const junkDrawersPatchValidator = getValidator(junkDrawersPatchSchema, dataValidator)
export const junkDrawersPatchResolver = resolve<JunkDrawersPatch, HookContext>({})

// Allow querying on any field from the main schema
const junkDrawersQueryProperties = junkDrawersSchema
export const junkDrawersQuerySchema = queryWrapper(junkDrawersQueryProperties)
export type JunkDrawersQuery = Static<typeof junkDrawersQuerySchema>
export const junkDrawersQueryValidator = getValidator(junkDrawersQuerySchema, queryValidator)
export const junkDrawersQueryResolver = resolve<JunkDrawersQuery, HookContext>({})
