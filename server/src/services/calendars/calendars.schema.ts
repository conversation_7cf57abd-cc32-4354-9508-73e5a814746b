// TypeBox schema for calendars service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const calendarsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
}, { additionalProperties: false })

export type Calendars = Static<typeof calendarsSchema>
export const calendarsValidator = getValidator(calendarsSchema, dataValidator)
export const calendarsResolver = resolve<Calendars, HookContext>({})
export const calendarsExternalResolver = resolve<Calendars, HookContext>({})

export const calendarsDataSchema = Type.Object({
  ...Type.Omit(calendarsSchema, ['_id']).properties
}, { additionalProperties: false })

export type CalendarsData = Static<typeof calendarsDataSchema>
export const calendarsDataValidator = getValidator(calendarsDataSchema, dataValidator)
export const calendarsDataResolver = resolve<CalendarsData, HookContext>({})

export const calendarsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(calendarsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type CalendarsPatch = Static<typeof calendarsPatchSchema>
export const calendarsPatchValidator = getValidator(calendarsPatchSchema, dataValidator)
export const calendarsPatchResolver = resolve<CalendarsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const calendarsQueryProperties = Type.Object({
  ...Type.Pick(calendarsSchema, ['_id', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const calendarsQuerySchema = queryWrapper(calendarsQueryProperties)
export type CalendarsQuery = Static<typeof calendarsQuerySchema>
export const calendarsQueryValidator = getValidator(calendarsQuerySchema, queryValidator)
export const calendarsQueryResolver = resolve<CalendarsQuery, HookContext>({})
