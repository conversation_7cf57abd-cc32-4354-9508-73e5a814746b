// TypeBox schema for claims service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull, queryWrapper } from '../../utils/common/typebox-schemas.js'

// Adjustment schema
const AdjSchema = Type.Object({
  adjBy: ObjectIdSchema(),
  adjAt: Type.Optional(Type.Any()),
  fp: ObjectIdSchema(),
  enrollment: Type.Optional(ObjectIdSchema()),
  ded: Type.Optional(Type.Number()),
  coins: Type.Optional(Type.Number()),
  copay: Type.Optional(Type.Number()),
  coverage: Type.Optional(ObjectIdSchema()),
  waived_ded: Type.Optional(Type.Number()),
  waived_coins: Type.Optional(Type.Number()),
  waived_copay: Type.Optional(Type.Number()),
  preventive: Type.Optional(Type.Boolean()),
  amount: Type.Optional(Type.Number()),
  qty: Type.Optional(Type.Number()),
  total: Type.Optional(Type.Number()),
  notes: Type.Optional(Type.String())
}, { additionalProperties: false })

// Entered by schema (simplified)
const EnteredBySchema = Type.Object({
  login: Type.Optional(ObjectIdSchema()),
  fingerprint: Type.Optional(ObjectIdSchema()),
  at: Type.Optional(Type.Any())
}, { additionalProperties: false })

// Log schema
const LogSchema = Type.Object({
  code: Type.Optional(Type.String()),
  standard: Type.Optional(Type.String())
}, { additionalProperties: false })

// Tax/Fee item schema
const TaxFeeItemSchema = Type.Object({
  name: Type.Optional(Type.String()),
  amount: Type.Optional(Type.Number())
}, { additionalProperties: false })

// Main data model schema
export const claimsSchema = Type.Object({
  _id: ObjectIdSchema(),
  visit: ObjectIdSchema(),
  plan: ObjectIdSchema(),
  patient: ObjectIdSchema(),
  person: ObjectIdSchema(),
  practitioner: Type.Optional(ObjectIdSchema()),
  provider: ObjectIdSchema(),
  procedure: Type.Optional(ObjectIdSchema()),
  med: Type.Optional(ObjectIdSchema()),
  coverage: Type.Optional(ObjectIdSchema()),
  date: Type.Optional(Type.Any()),
  misc: Type.Optional(Type.String()),
  enteredBy: Type.Optional(EnteredBySchema),
  log: Type.Optional(LogSchema),
  category: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  notes: Type.Optional(Type.String()),
  preventive: Type.Optional(Type.Boolean()),
  adj: Type.Optional(AdjSchema),
  // Paid fields (simplified)
  paid: Type.Optional(Type.Number()),
  paidAt: Type.Optional(Type.Any()),
  paidBy: Type.Optional(ObjectIdSchema()),
  paidMethod: Type.Optional(Type.String()),
  paidNotes: Type.Optional(Type.String()),
  // Amounts
  amount: Type.Optional(Type.Number()),
  subtotal: Type.Optional(Type.Number()),
  total: Type.Optional(Type.Number()),
  qty: Type.Optional(Type.Number()),
  balanceSyncedAt: Type.Optional(Type.Any()),
  balance: Type.Optional(Type.Number()),
  adjHistory: Type.Optional(Type.Array(AdjSchema)),
  taxes: Type.Optional(Type.Record(Type.String(), TaxFeeItemSchema)),
  fees: Type.Optional(Type.Record(Type.String(), TaxFeeItemSchema)),
  ...commonFields
}, { additionalProperties: false })

export type Claims = Static<typeof claimsSchema>
export const claimsValidator = getValidator(claimsSchema, dataValidator)
export const claimsResolver = resolve<Claims, HookContext>({})
export const claimsExternalResolver = resolve<Claims, HookContext>({})

// Schema for creating new data
export const claimsDataSchema = Type.Object({
  ...Type.Omit(claimsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ClaimsData = Static<typeof claimsDataSchema>
export const claimsDataValidator = getValidator(claimsDataSchema, dataValidator)
export const claimsDataResolver = resolve<ClaimsData, HookContext>({})

// Schema for updating existing data
export const claimsPatchSchema = Type.Object({
  visit: Type.Optional(ObjectIdSchema()),
  plan: Type.Optional(ObjectIdSchema()),
  patient: Type.Optional(ObjectIdSchema()),
  person: Type.Optional(ObjectIdSchema()),
  practitioner: Type.Optional(ObjectIdSchema()),
  provider: Type.Optional(ObjectIdSchema()),
  procedure: Type.Optional(ObjectIdSchema()),
  med: Type.Optional(ObjectIdSchema()),
  coverage: Type.Optional(ObjectIdSchema()),
  date: Type.Optional(Type.Any()),
  misc: Type.Optional(Type.String()),
  enteredBy: Type.Optional(EnteredBySchema),
  log: Type.Optional(LogSchema),
  category: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  notes: Type.Optional(Type.String()),
  preventive: Type.Optional(Type.Boolean()),
  adj: Type.Optional(AdjSchema),
  paid: Type.Optional(Type.Number()),
  paidAt: Type.Optional(Type.Any()),
  paidBy: Type.Optional(ObjectIdSchema()),
  paidMethod: Type.Optional(Type.String()),
  paidNotes: Type.Optional(Type.String()),
  amount: Type.Optional(Type.Number()),
  subtotal: Type.Optional(Type.Number()),
  total: Type.Optional(Type.Number()),
  qty: Type.Optional(Type.Number()),
  balanceSyncedAt: Type.Optional(Type.Any()),
  balance: Type.Optional(Type.Number()),
  adjHistory: Type.Optional(Type.Array(AdjSchema)),
  taxes: Type.Optional(Type.Record(Type.String(), TaxFeeItemSchema)),
  fees: Type.Optional(Type.Record(Type.String(), TaxFeeItemSchema)),
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $push: Type.Optional(Type.Object({
    adjHistory: Type.Optional(Type.Union([
      AdjSchema,
      Type.Object({
        $each: Type.Optional(Type.Array(AdjSchema))
      ,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
    ]))
  }, { additionalProperties: false }))
}, { additionalProperties: false })

export type ClaimsPatch = Static<typeof claimsPatchSchema>
export const claimsPatchValidator = getValidator(claimsPatchSchema, dataValidator)
export const claimsPatchResolver = resolve<ClaimsPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const claimsQueryProperties = Type.Object({
  ...Type.Pick(claimsSchema, ['_id', 'visit', 'plan', 'patient', 'person', 'practitioner', 'provider', 'procedure', 'med', 'coverage', 'paidBy', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })

export const claimsQuerySchema = queryWrapper(claimsQueryProperties)

export type ClaimsQuery = Static<typeof claimsQuerySchema>
export const claimsQueryValidator = getValidator(claimsQuerySchema, queryValidator)
export const claimsQueryResolver = resolve<ClaimsQuery, HookContext>({})

// Export for backward compatibility with business logic
export const adjKeys = ['amount', 'copay', 'coinsurance', 'deductible']
