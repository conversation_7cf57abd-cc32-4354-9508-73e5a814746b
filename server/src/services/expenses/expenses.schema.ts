// TypeBox schema for expenses service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const expensesSchema = Type.Object({
  _id: ObjectIdSchema(),
  account: ObjectIdSchema(),
  amount: Type.Number(),
  category: Type.Optional(Type.String()),
  vendor: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  date: Type.Optional(Type.Any()),
  receipt: Type.Optional(ObjectIdSchema()),
  approved: Type.Optional(Type.Boolean()),
  approvedBy: Type.Optional(ObjectIdSchema()),
  reimbursed: Type.Optional(Type.Boolean()),
  reimbursedAt: Type.Optional(Type.Any()),
  tags: Type.Optional(Type.Array(Type.String())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
}, { additionalProperties: false })

export type Expenses = Static<typeof expensesSchema>
export const expensesValidator = getValidator(expensesSchema, dataValidator)
export const expensesResolver = resolve<Expenses, HookContext>({})
export const expensesExternalResolver = resolve<Expenses, HookContext>({})

export const expensesDataSchema = Type.Object({
  ...Type.Omit(expensesSchema, ['_id']).properties
}, { additionalProperties: false })

export type ExpensesData = Static<typeof expensesDataSchema>
export const expensesDataValidator = getValidator(expensesDataSchema, dataValidator)
export const expensesDataResolver = resolve<ExpensesData, HookContext>({})

export const expensesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(expensesSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $inc: Type.Optional(Type.Record(Type.String(), Type.Number())),
}, { additionalProperties: false })
export type ExpensesPatch = Static<typeof expensesPatchSchema>
export const expensesPatchValidator = getValidator(expensesPatchSchema, dataValidator)
export const expensesPatchResolver = resolve<ExpensesPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const expensesQueryProperties = Type.Object({
  ...Type.Pick(expensesSchema, ['_id', 'account', 'receipt', 'approvedBy', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const expensesQuerySchema = queryWrapper(expensesQueryProperties)
export type ExpensesQuery = Static<typeof expensesQuerySchema>
export const expensesQueryValidator = getValidator(expensesQuerySchema, queryValidator)
export const expensesQueryResolver = resolve<ExpensesQuery, HookContext>({})
