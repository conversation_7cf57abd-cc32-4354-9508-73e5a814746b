// TypeBox schema for comps service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const compsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  key: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Comps = Static<typeof compsSchema>
export const compsValidator = getValidator(compsSchema, dataValidator)
export const compsResolver = resolve<Comps, HookContext>({})
export const compsExternalResolver = resolve<Comps, HookContext>({})

export const compsDataSchema = Type.Object({
  ...Type.Omit(compsSchema, ['_id']).properties
}, { additionalProperties: false })

export type CompsData = Static<typeof compsDataSchema>
export const compsDataValidator = getValidator(compsDataSchema, dataValidator)
export const compsDataResolver = resolve<CompsData, HookContext>({})

export const compsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(compsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type CompsPatch = Static<typeof compsPatchSchema>
export const compsPatchValidator = getValidator(compsPatchSchema, dataValidator)
export const compsPatchResolver = resolve<CompsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const compsQueryProperties = Type.Object({
  ...Type.Pick(compsSchema, ['_id', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const compsQuerySchema = queryWrapper(compsQueryProperties)
export type CompsQuery = Static<typeof compsQuerySchema>
export const compsQueryValidator = getValidator(compsQuerySchema, queryValidator)
export const compsQueryResolver = resolve<CompsQuery, HookContext>({})
