// TypeBox schema for groups service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull, queryWrapper } from '../../utils/common/typebox-schemas.js'

// Main data model schema
export const groupsSchema = Type.Object({
  _id: ObjectIdSchema(),
  org: ObjectIdSchema(),
  name: Type.String(),
  description: Type.Optional(Type.String()),
  applyUcan: Type.Optional(Type.String()),
  healthPlans: Type.Optional(Type.Array(ObjectIdSchema())),
  planClass: Type.Optional(Type.Boolean()),
  memberCount: Type.Optional(Type.Number()),
  memberCountAt: Type.Optional(Type.Any()),
  ...commonFields
,
  // Missing fields from old schema
  key: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Groups = Static<typeof groupsSchema>
export const groupsValidator = getValidator(groupsSchema, dataValidator)
export const groupsResolver = resolve<Groups, HookContext>({})
export const groupsExternalResolver = resolve<Groups, HookContext>({})

// Schema for creating new data
export const groupsDataSchema = Type.Object({
  ...Type.Omit(groupsSchema, ['_id']).properties
}, { additionalProperties: false })

export type GroupsData = Static<typeof groupsDataSchema>
export const groupsDataValidator = getValidator(groupsDataSchema, dataValidator)
export const groupsDataResolver = resolve<GroupsData, HookContext>({})

const pushPull = [{ path: 'members', type: ObjectIdSchema() }]
// Schema for updating existing data
export const groupsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(groupsSchema, ['_id'])).properties,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet(pushPull)),
  $pull: Type.Optional(pull(pushPull)),
}, { additionalProperties: false })

export type GroupsPatch = Static<typeof groupsPatchSchema>
export const groupsPatchValidator = getValidator(groupsPatchSchema, dataValidator)
export const groupsPatchResolver = resolve<GroupsPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const groupsQueryProperties = Type.Object({
  ...Type.Pick(groupsSchema, ['_id', 'org', 'members', 'managers', 'plans', 'coverages', 'enrollments', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })

export const groupsQuerySchema = queryWrapper(groupsQueryProperties)

export type GroupsQuery = Static<typeof groupsQuerySchema>
export const groupsQueryValidator = getValidator(groupsQuerySchema, queryValidator)
export const groupsQueryResolver = resolve<GroupsQuery, HookContext>({})
