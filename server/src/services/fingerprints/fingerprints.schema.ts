// TypeBox schema for fingerprints service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const fingerprintsSchema = Type.Object({
  _id: ObjectIdSchema(),
  hash: Type.Optional(Type.String()),
  userAgent: Type.Optional(Type.String()),
  ip: Type.Optional(Type.String()),
  browser: Type.Optional(Type.String()),
  os: Type.Optional(Type.String()),
  device: Type.Optional(Type.String()),
  screen: Type.Optional(Type.String()),
  timezone: Type.Optional(Type.String()),
  language: Type.Optional(Type.String()),
  plugins: Type.Optional(Type.Array(Type.String())),
  fonts: Type.Optional(Type.Array(Type.String())),
  canvas: Type.Optional(Type.String()),
  webgl: Type.Optional(Type.String()),
  audio: Type.Optional(Type.String()),
  lastSeen: Type.Optional(Type.Any()),
  visits: Type.Optional(Type.Number()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  turnstile: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  additionalProperties: Type.Optional(Type.String()),
  properties: Type.Optional(Type.Array(Type.String())),
  success: Type.Optional(Type.Boolean()),
}, { additionalProperties: false })

export type Fingerprints = Static<typeof fingerprintsSchema>
export const fingerprintsValidator = getValidator(fingerprintsSchema, dataValidator)
export const fingerprintsResolver = resolve<Fingerprints, HookContext>({})
export const fingerprintsExternalResolver = resolve<Fingerprints, HookContext>({})

export const fingerprintsDataSchema = Type.Object({
  ...Type.Omit(fingerprintsSchema, ['_id']).properties
}, { additionalProperties: false })

export type FingerprintsData = Static<typeof fingerprintsDataSchema>
export const fingerprintsDataValidator = getValidator(fingerprintsDataSchema, dataValidator)
export const fingerprintsDataResolver = resolve<FingerprintsData, HookContext>({})

export const fingerprintsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(fingerprintsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type FingerprintsPatch = Static<typeof fingerprintsPatchSchema>
export const fingerprintsPatchValidator = getValidator(fingerprintsPatchSchema, dataValidator)
export const fingerprintsPatchResolver = resolve<FingerprintsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const fingerprintsQueryProperties = Type.Object({
  ...Type.Pick(fingerprintsSchema, ['_id', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const fingerprintsQuerySchema = queryWrapper(fingerprintsQueryProperties)
export type FingerprintsQuery = Static<typeof fingerprintsQuerySchema>
export const fingerprintsQueryValidator = getValidator(fingerprintsQuerySchema, queryValidator)
export const fingerprintsQueryResolver = resolve<FingerprintsQuery, HookContext>({})
