// TypeBox schema for caps service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const capsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  limit: Type.Optional(Type.Number()),
  used: Type.Optional(Type.Number()),
  remaining: Type.Optional(Type.Number()),
  period: Type.Optional(Type.String()),
  resetDate: Type.Optional(Type.Any()),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  subject: Type.Optional(Type.String()),
  subjectService: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Caps = Static<typeof capsSchema>
export const capsValidator = getValidator(capsSchema, dataValidator)
export const capsResolver = resolve<Caps, HookContext>({})
export const capsExternalResolver = resolve<Caps, HookContext>({})

export const capsDataSchema = Type.Object({
  ...Type.Omit(capsSchema, ['_id']).properties
}, { additionalProperties: false })

export type CapsData = Static<typeof capsDataSchema>
export const capsDataValidator = getValidator(capsDataSchema, dataValidator)
export const capsDataResolver = resolve<CapsData, HookContext>({})

export const capsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(capsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $pull: Type.Optional(pull([])),
}, { additionalProperties: false })
export type CapsPatch = Static<typeof capsPatchSchema>
export const capsPatchValidator = getValidator(capsPatchSchema, dataValidator)
export const capsPatchResolver = resolve<CapsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const capsQueryProperties = Type.Object({
  ...Type.Pick(capsSchema, ['_id', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const capsQuerySchema = queryWrapper(capsQueryProperties)
export type CapsQuery = Static<typeof capsQuerySchema>
export const capsQueryValidator = getValidator(capsQuerySchema, queryValidator)
export const capsQueryResolver = resolve<CapsQuery, HookContext>({})
