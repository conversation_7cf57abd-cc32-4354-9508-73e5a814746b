// TypeBox schema for plans service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ImageSchema, TaxSchema, addToSet, pull, queryWrapper } from '../../utils/common/typebox-schemas.js'

// Main data model schema
export const plansSchema = Type.Object({
  _id: ObjectIdSchema(),
  org: ObjectIdSchema(),
  name: Type.String(),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  coverage: Type.Optional(ObjectIdSchema()),
  coverages: Type.Optional(Type.Array(ObjectIdSchema())),
  group: Type.Optional(ObjectIdSchema()),
  effectiveDate: Type.Optional(Type.Any()),
  terminationDate: Type.Optional(Type.Any()),
  renewalDate: Type.Optional(Type.Any()),
  premium: Type.Optional(Type.Number()),
  employerContribution: Type.Optional(Type.Number()),
  employeeContribution: Type.Optional(Type.Number()),
  deductible: Type.Optional(Type.Number()),
  outOfPocketMax: Type.Optional(Type.Number()),
  copay: Type.Optional(Type.Number()),
  coinsurance: Type.Optional(Type.Number()),
  network: Type.Optional(ObjectIdSchema()),
  networks: Type.Optional(Type.Array(ObjectIdSchema())),
  providers: Type.Optional(Type.Array(ObjectIdSchema())),
  formulary: Type.Optional(ObjectIdSchema()),
  benefits: Type.Optional(Type.Record(Type.String(), Type.Any())),
  exclusions: Type.Optional(Type.Array(Type.String())),
  limitations: Type.Optional(Type.Array(Type.String())),
  documents: Type.Optional(Type.Array(ImageSchema)),
  brochure: Type.Optional(ImageSchema),
  summaryOfBenefits: Type.Optional(ImageSchema),
  taxes: Type.Optional(TaxSchema),
  active: Type.Optional(Type.Boolean()),
  public: Type.Optional(Type.Boolean()),
  template: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  parent: Type.Optional(Type.String()),
  doc: Type.Optional(Type.String()),
  spd: Type.Optional(Type.String()),
  ale: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Plans = Static<typeof plansSchema>
export const plansValidator = getValidator(plansSchema, dataValidator)
export const plansResolver = resolve<Plans, HookContext>({})
export const plansExternalResolver = resolve<Plans, HookContext>({})

// Schema for creating new data
export const plansDataSchema = Type.Object({
  ...Type.Omit(plansSchema, ['_id']).properties
}, { additionalProperties: false })

export type PlansData = Static<typeof plansDataSchema>
export const plansDataValidator = getValidator(plansDataSchema, dataValidator)
export const plansDataResolver = resolve<PlansData, HookContext>({})

// Schema for updating existing data
export const plansPatchSchema = Type.Object({
  org: Type.Optional(ObjectIdSchema()),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  coverage: Type.Optional(ObjectIdSchema()),
  coverages: Type.Optional(Type.Array(ObjectIdSchema())),
  group: Type.Optional(ObjectIdSchema()),
  effectiveDate: Type.Optional(Type.Any()),
  terminationDate: Type.Optional(Type.Any()),
  renewalDate: Type.Optional(Type.Any()),
  premium: Type.Optional(Type.Number()),
  employerContribution: Type.Optional(Type.Number()),
  employeeContribution: Type.Optional(Type.Number()),
  deductible: Type.Optional(Type.Number()),
  outOfPocketMax: Type.Optional(Type.Number()),
  copay: Type.Optional(Type.Number()),
  coinsurance: Type.Optional(Type.Number()),
  network: Type.Optional(ObjectIdSchema()),
  networks: Type.Optional(Type.Array(ObjectIdSchema())),
  providers: Type.Optional(Type.Array(ObjectIdSchema())),
  formulary: Type.Optional(ObjectIdSchema()),
  benefits: Type.Optional(Type.Record(Type.String(), Type.Any())),
  exclusions: Type.Optional(Type.Array(Type.String())),
  limitations: Type.Optional(Type.Array(Type.String())),
  documents: Type.Optional(Type.Array(ImageSchema)),
  brochure: Type.Optional(ImageSchema),
  summaryOfBenefits: Type.Optional(ImageSchema),
  taxes: Type.Optional(TaxSchema),
  active: Type.Optional(Type.Boolean()),
  public: Type.Optional(Type.Boolean()),
  template: Type.Optional(Type.Boolean()),
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $push: Type.Optional(Type.Object({
    coverages: Type.Optional(ObjectIdSchema()),
    networks: Type.Optional(ObjectIdSchema()),
    providers: Type.Optional(ObjectIdSchema()),
    exclusions: Type.Optional(Type.String()),
    limitations: Type.Optional(Type.String()),
    documents: Type.Optional(ImageSchema)
  ,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
  $inc: Type.Optional(Type.Record(Type.String(), Type.Number())),
}, { additionalProperties: false })),
  $pull: Type.Optional(Type.Object({
    coverages: Type.Optional(ObjectIdSchema()),
    networks: Type.Optional(ObjectIdSchema()),
    providers: Type.Optional(ObjectIdSchema()),
    exclusions: Type.Optional(Type.String()),
    limitations: Type.Optional(Type.String()),
    documents: Type.Optional(ImageSchema)
  }, { additionalProperties: false }))
}, { additionalProperties: false })

export type PlansPatch = Static<typeof plansPatchSchema>
export const plansPatchValidator = getValidator(plansPatchSchema, dataValidator)
export const plansPatchResolver = resolve<PlansPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const plansQueryProperties = Type.Object({
  ...Type.Pick(plansSchema, ['_id', 'org', 'coverage', 'coverages', 'group', 'network', 'networks', 'providers', 'formulary', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })

export const plansQuerySchema = queryWrapper(plansQueryProperties)

export type PlansQuery = Static<typeof plansQuerySchema>
export const plansQueryValidator = getValidator(plansQuerySchema, queryValidator)
export const plansQueryResolver = resolve<PlansQuery, HookContext>({})
