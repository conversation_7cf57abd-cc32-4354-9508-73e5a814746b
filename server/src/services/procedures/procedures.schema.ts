// TypeBox schema for procedures service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, queryWrapper } from '../../utils/common/typebox-schemas.js'

// Code schema for procedure codes
const CodeSchema = Type.Object({
  standard: Type.Optional(Type.String()),
  code: Type.Optional(Type.String())
}, { additionalProperties: false })

// Main data model schema
export const proceduresSchema = Type.Object({
  _id: ObjectIdSchema(),
  standard: Type.Optional(Type.String()),
  level: Type.Optional(Type.Union([Type.Literal('1'), Type.Literal('2')])),
  subLevel: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  pcc: Type.Optional(Type.String()), // procedure code category
  parent: Type.Optional(Type.String()),
  code: Type.Optional(Type.String()),
  codes: Type.Optional(Type.Array(CodeSchema)),
  name: Type.Optional(Type.String()),
  names: Type.Optional(Type.Array(Type.String())),
  layName: Type.Optional(Type.String()),
  layDescription: Type.Optional(Type.String()),
  descriptions: Type.Optional(Type.Array(Type.String())),
  description: Type.Optional(Type.String()),
  synonyms: Type.Optional(Type.String()),
  ...commonFields
}, { additionalProperties: false })

export type Procedures = Static<typeof proceduresSchema>
export const proceduresValidator = getValidator(proceduresSchema, dataValidator)
export const proceduresResolver = resolve<Procedures, HookContext>({})
export const proceduresExternalResolver = resolve<Procedures, HookContext>({})

// Schema for creating new data
export const proceduresDataSchema = Type.Object({
  ...Type.Omit(proceduresSchema, ['_id']).properties
}, { additionalProperties: false })

export type ProceduresData = Static<typeof proceduresDataSchema>
export const proceduresDataValidator = getValidator(proceduresDataSchema, dataValidator)
export const proceduresDataResolver = resolve<ProceduresData, HookContext>({})

// Schema for updating existing data
export const proceduresPatchSchema = Type.Object({
  standard: Type.Optional(Type.String()),
  level: Type.Optional(Type.Union([Type.Literal('1'), Type.Literal('2')])),
  subLevel: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  pcc: Type.Optional(Type.String()),
  parent: Type.Optional(Type.String()),
  code: Type.Optional(Type.String()),
  codes: Type.Optional(Type.Array(CodeSchema)),
  name: Type.Optional(Type.String()),
  names: Type.Optional(Type.Array(Type.String())),
  layName: Type.Optional(Type.String()),
  layDescription: Type.Optional(Type.String()),
  descriptions: Type.Optional(Type.Array(Type.String())),
  description: Type.Optional(Type.String()),
  synonyms: Type.Optional(Type.String()),
  $set: Type.Optional(Type.Record(Type.String(), Type.Any())),
  $unset: Type.Optional(Type.Record(Type.String(), Type.Any()))
,
  // Missing MongoDB operators
  $addToSet: Type.Optional(addToSet([])),
}, { additionalProperties: false })

export type ProceduresPatch = Static<typeof proceduresPatchSchema>
export const proceduresPatchValidator = getValidator(proceduresPatchSchema, dataValidator)
export const proceduresPatchResolver = resolve<ProceduresPatch, HookContext>({})

// Schema for allowed query properties
// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const proceduresQueryProperties = Type.Object({
  ...Type.Pick(proceduresSchema, ['_id', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })

export const proceduresQuerySchema = queryWrapper(proceduresQueryProperties)

export type ProceduresQuery = Static<typeof proceduresQuerySchema>
export const proceduresQueryValidator = getValidator(proceduresQuerySchema, queryValidator)
export const proceduresQueryResolver = resolve<ProceduresQuery, HookContext>({})
