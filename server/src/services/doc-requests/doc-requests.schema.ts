// TypeBox schema for doc-requests service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const docRequestsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  types: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type DocRequests = Static<typeof docRequestsSchema>
export const docRequestsValidator = getValidator(docRequestsSchema, dataValidator)
export const docRequestsResolver = resolve<DocRequests, HookContext>({})
export const docRequestsExternalResolver = resolve<DocRequests, HookContext>({})

export const docRequestsDataSchema = Type.Object({
  ...Type.Omit(docRequestsSchema, ['_id']).properties
}, { additionalProperties: false })

export type DocRequestsData = Static<typeof docRequestsDataSchema>
export const docRequestsDataValidator = getValidator(docRequestsDataSchema, dataValidator)
export const docRequestsDataResolver = resolve<DocRequestsData, HookContext>({})

export const docRequestsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(docRequestsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type DocRequestsPatch = Static<typeof docRequestsPatchSchema>
export const docRequestsPatchValidator = getValidator(docRequestsPatchSchema, dataValidator)
export const docRequestsPatchResolver = resolve<DocRequestsPatch, HookContext>({})

// Allow querying on any field from the main schema
const docRequestsQueryProperties = docRequestsSchema
export const docRequestsQuerySchema = queryWrapper(docRequestsQueryProperties)
export type DocRequestsQuery = Static<typeof docRequestsQuerySchema>
export const docRequestsQueryValidator = getValidator(docRequestsQuerySchema, queryValidator)
export const docRequestsQueryResolver = resolve<DocRequestsQuery, HookContext>({})

// Export for backward compatibility with business logic
export const eeFactsSchema = Type.Object({
  name: Type.Optional(Type.String()),
  value: Type.Optional(Type.Any()),
  type: Type.Optional(Type.String())
}, { additionalProperties: false })
