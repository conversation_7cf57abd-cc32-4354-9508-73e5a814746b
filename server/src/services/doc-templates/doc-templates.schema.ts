// TypeBox schema for doc-templates service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const docTemplatesSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
}, { additionalProperties: false })

export type DocTemplates = Static<typeof docTemplatesSchema>
export const docTemplatesValidator = getValidator(docTemplatesSchema, dataValidator)
export const docTemplatesResolver = resolve<DocTemplates, HookContext>({})
export const docTemplatesExternalResolver = resolve<DocTemplates, HookContext>({})

export const docTemplatesDataSchema = Type.Object({
  ...Type.Omit(docTemplatesSchema, ['_id']).properties
}, { additionalProperties: false })

export type DocTemplatesData = Static<typeof docTemplatesDataSchema>
export const docTemplatesDataValidator = getValidator(docTemplatesDataSchema, dataValidator)
export const docTemplatesDataResolver = resolve<DocTemplatesData, HookContext>({})

export const docTemplatesPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(docTemplatesSchema, ['_id'])).properties
}, { additionalProperties: false })
export type DocTemplatesPatch = Static<typeof docTemplatesPatchSchema>
export const docTemplatesPatchValidator = getValidator(docTemplatesPatchSchema, dataValidator)
export const docTemplatesPatchResolver = resolve<DocTemplatesPatch, HookContext>({})

// Allow querying on any field from the main schema
const docTemplatesQueryProperties = docTemplatesSchema
export const docTemplatesQuerySchema = queryWrapper(docTemplatesQueryProperties)
export type DocTemplatesQuery = Static<typeof docTemplatesQuerySchema>
export const docTemplatesQueryValidator = getValidator(docTemplatesQuerySchema, queryValidator)
export const docTemplatesQueryResolver = resolve<DocTemplatesQuery, HookContext>({})
