// TypeBox schema for ims service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, addToSet, pull, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const imsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.Optional(Type.String()),
  description: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  status: Type.Optional(Type.String()),
  data: Type.Optional(Type.Record(Type.String(), Type.Any())),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
,
  // Missing fields from old schema
  subject: Type.Optional(Type.String()),
  subjectService: Type.Optional(Type.String()),
}, { additionalProperties: false })

export type Ims = Static<typeof imsSchema>
export const imsValidator = getValidator(imsSchema, dataValidator)
export const imsResolver = resolve<Ims, HookContext>({})
export const imsExternalResolver = resolve<Ims, HookContext>({})

export const imsDataSchema = Type.Object({
  ...Type.Omit(imsSchema, ['_id']).properties
}, { additionalProperties: false })

export type ImsData = Static<typeof imsDataSchema>
export const imsDataValidator = getValidator(imsDataSchema, dataValidator)
export const imsDataResolver = resolve<ImsData, HookContext>({})

export const imsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(imsSchema, ['_id'])).properties
,
  // Missing MongoDB operators
  $pull: Type.Optional(pull([])),
  $addToSet: Type.Optional(addToSet([])),
}, { additionalProperties: false })
export type ImsPatch = Static<typeof imsPatchSchema>
export const imsPatchValidator = getValidator(imsPatchSchema, dataValidator)
export const imsPatchResolver = resolve<ImsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const imsQueryProperties = Type.Object({
  ...Type.Pick(imsSchema, ['_id', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const imsQuerySchema = queryWrapper(imsQueryProperties)
export type ImsQuery = Static<typeof imsQuerySchema>
export const imsQueryValidator = getValidator(imsQuerySchema, queryValidator)
export const imsQueryResolver = resolve<ImsQuery, HookContext>({})
