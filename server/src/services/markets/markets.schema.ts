// TypeBox schema for markets service
import { resolve } from '@feathersjs/schema'
import { Type, getValidator, ObjectIdSchema } from '@feathersjs/typebox'
import type { Static } from '@feathersjs/typebox'
import type { HookContext } from '../../declarations.js'
import { dataValidator, queryValidator } from '../../validators.js'
import { commonFields, ServiceAddressSchema, queryWrapper } from '../../utils/common/typebox-schemas.js'

export const marketsSchema = Type.Object({
  _id: ObjectIdSchema(),
  name: Type.String(),
  state: Type.Optional(Type.String()),
  region: Type.Optional(Type.String()),
  type: Type.Optional(Type.String()),
  category: Type.Optional(Type.String()),
  plans: Type.Optional(Type.Array(ObjectIdSchema())),
  coverages: Type.Optional(Type.Array(ObjectIdSchema())),
  providers: Type.Optional(Type.Array(ObjectIdSchema())),
  networks: Type.Optional(Type.Array(ObjectIdSchema())),
  serviceArea: Type.Optional(Type.Array(ServiceAddressSchema)),
  counties: Type.Optional(Type.Array(Type.String())),
  zipCodes: Type.Optional(Type.Array(Type.String())),
  effectiveDate: Type.Optional(Type.Any()),
  terminationDate: Type.Optional(Type.Any()),
  openEnrollmentStart: Type.Optional(Type.Any()),
  openEnrollmentEnd: Type.Optional(Type.Any()),
  specialEnrollmentPeriods: Type.Optional(Type.Array(Type.Object({
    name: Type.Optional(Type.String()),
    start: Type.Optional(Type.Any()),
    end: Type.Optional(Type.Any()),
    reason: Type.Optional(Type.String())
  }, { additionalProperties: false }))),
  active: Type.Optional(Type.Boolean()),
  ...commonFields
}, { additionalProperties: false })

export type Markets = Static<typeof marketsSchema>
export const marketsValidator = getValidator(marketsSchema, dataValidator)
export const marketsResolver = resolve<Markets, HookContext>({})
export const marketsExternalResolver = resolve<Markets, HookContext>({})

export const marketsDataSchema = Type.Object({
  ...Type.Omit(marketsSchema, ['_id']).properties
}, { additionalProperties: false })

export type MarketsData = Static<typeof marketsDataSchema>
export const marketsDataValidator = getValidator(marketsDataSchema, dataValidator)
export const marketsDataResolver = resolve<MarketsData, HookContext>({})

export const marketsPatchSchema = Type.Object({
  ...Type.Partial(Type.Omit(marketsSchema, ['_id'])).properties
}, { additionalProperties: false })
export type MarketsPatch = Static<typeof marketsPatchSchema>
export const marketsPatchValidator = getValidator(marketsPatchSchema, dataValidator)
export const marketsPatchResolver = resolve<MarketsPatch, HookContext>({})

// Allow querying on any field from the main schema
// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const marketsQueryProperties = Type.Object({
  ...Type.Pick(marketsSchema, ['_id', 'plans', 'coverages', 'providers', 'networks', 'createdBy', 'updatedBy']).properties
}, { additionalProperties: true })
export const marketsQuerySchema = queryWrapper(marketsQueryProperties)
export type MarketsQuery = Static<typeof marketsQuerySchema>
export const marketsQueryValidator = getValidator(marketsQuerySchema, queryValidator)
export const marketsQueryResolver = resolve<MarketsQuery, HookContext>({})
