#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Find all service schema files
const servicesDir = 'server/src/services';
const services = fs.readdirSync(servicesDir).filter(dir => {
  const schemaFile = path.join(servicesDir, dir, `${dir}.schema.ts`);
  return fs.existsSync(schemaFile);
});

console.log(`Fixing syntax errors in ${services.length} services`);

services.forEach(serviceName => {
  const schemaFile = path.join(servicesDir, serviceName, `${serviceName}.schema.ts`);
  let content = fs.readFileSync(schemaFile, 'utf8');
  
  // Look for the broken pattern where there are fragments left
  const brokenPattern = /\/\/ Allow querying on any field from the main schema\s*\n\s*\.\.\.Type\.Pick\([^}]+\)\.properties[^}]*\}/g;
  
  if (brokenPattern.test(content)) {
    console.log(`❌ Found broken pattern in ${serviceName}, needs manual fix`);
    return;
  }
  
  // Look for missing const declaration pattern
  const missingConstPattern = /\/\/ Allow querying on any field from the main schema\s*\n\s*\.\.\.Type\.Pick\([^}]+\)\.properties[^}]*\n\s*\}, \{ additionalProperties: true \}\)/g;
  
  if (missingConstPattern.test(content)) {
    console.log(`🔧 Fixing missing const declaration in ${serviceName}`);
    
    // Extract the Pick content
    const match = content.match(/\.\.\.Type\.Pick\((\w+Schema), \[([^\]]+)\]\)\.properties/);
    if (match) {
      const schemaName = match[1];
      const fields = match[2];
      
      const capitalizedServiceName = serviceName.charAt(0).toUpperCase() + serviceName.slice(1);
      const queryPropsName = `${serviceName}QueryProperties`;
      
      const replacement = `// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const ${queryPropsName} = Type.Object({
  ...Type.Pick(${schemaName}, [${fields}]).properties
}, { additionalProperties: true })`;
      
      content = content.replace(missingConstPattern, replacement);
      fs.writeFileSync(schemaFile, content);
      console.log(`✅ Fixed ${serviceName}`);
    }
  }
});

console.log('✅ Syntax error fix complete!');
