<template>
  <div class="_fw">
    <div class="row">
      <div class="col-12 col-md-6 q-pa-sm">
        <div class="__c">
        <issue-card editing :model-value="issue"></issue-card>
        </div>
      </div>
      <div class="col-12 col-md-6 q-pa-sm">
        <div class="__c">
          <append-issue v-if="issue.type === 'complaint'" :model-value="issue"></append-issue>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import IssueCard from 'components/issues/cards/IssueCard.vue';
  import AppendIssue from 'components/issues/forms/AppendIssue.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {useIssues} from 'stores/issues';
  import {computed} from 'vue';

  const store = useIssues();

  const props = defineProps({
    modelValue: { required: true }
  })

  const { item: issue } = idGet({
    store,
    value: computed(() => props.modelValue),
    useAtcStore
  })
</script>

<style lang="scss" scoped>
.__c {
  width: 100%;
  border-radius: 12px;
  box-shadow: 0 2px 8px -3px #999;
  background: white;
  padding: 30px 10px;
}
</style>
