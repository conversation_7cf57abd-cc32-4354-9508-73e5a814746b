<template>
  <div class="flex items-center">
    <q-img v-if="oldImg.url" class="h50 w50" fit="contain" :src="oldImg.url"></q-img>
    <div v-else class="q-pa-xs font-7-8r">N/A</div>

    <div class="q-px-sm">
      <q-icon size="25px" name="mdi-arrow-right-bold"></q-icon>
    </div>

    <q-img v-if="newImg.url" class="h50 w50" fit="contain" :src="newImg.url"></q-img>
    <div v-else class="q-pa-xs font-7-8r">N/A</div>

  </div>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {useUploads} from 'stores/uploads';
  import {computed} from 'vue';
  import {fakeId} from 'src/utils/global-methods';
  import {useAtcStore} from 'src/stores/atc-store';
  const uploadStore = useUploads();

  const props = defineProps({
    nv: { required: true },
    ov: { required: true },
  })

  const { item:newImg } = idGet({
    store: uploadStore,
    value: computed(() => props.nv?._id ? props.nv : { _id: fakeId, ...props.nv }),
    useAtcStore
  })
  const { item:oldImg } = idGet({
    store: uploadStore,
    value: computed(() => props.ov?._id ? props.ov : { _id: fakeId, ...props.ov }),
    useAtcStore
  })
</script>

<style lang="scss" scoped>

</style>
