<template>
  <div class="flex items-center">
    <q-video class="__vid" ratio="1.7777" v-if="oldVid.url" :src="oldVid.url"></q-video>
    <div v-else class="q-pa-xs font-7-8r">N/A</div>

    <div class="q-px-sm">
      <q-icon size="25px" name="mdi-arrow-right-bold"></q-icon>
    </div>

    <q-video class="__vid" v-if="newVid.url" ratio="1.7777" :src="newVid.url"></q-video>
    <div v-else class="q-pa-xs font-7-8r">N/A</div>

  </div>
</template>

<script setup>

  import {idGet} from 'src/utils/id-get';
  import {useUploads} from 'stores/uploads';
  import {computed} from 'vue';
  import {fakeId} from 'src/utils/global-methods';
  import {useAtcStore} from 'src/stores/atc-store';
  const uploadStore = useUploads();

  const props = defineProps({
    nv: { required: true },
    ov: { required: true },
  })

  const { item:newVid } = idGet({
    store: uploadStore,
    value: computed(() => props.nv?._id ? props.nv : { _id: fakeId, ...props.nv }),
    useAtcStore
  })
  const { item:oldVid } = idGet({
    store: uploadStore,
    value: computed(() => props.ov?._id ? props.ov : { _id: fakeId, ...props.ov }),
    useAtcStore
  })
</script>

<style lang="scss" scoped>

  .__vid {
    width: 177px;
    height: 100px;
    border-radius: 8px;
  }
</style>
