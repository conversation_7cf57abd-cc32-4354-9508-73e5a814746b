<template>
  <div class="_fw _bg_ow">
    <div class="font-1r tw-six q-py-md q-px-sm">Review/Enter your income for accurate recommendations and tax breakdown</div>
    <div class="row justify-center">
      <div class="_cent">
        <div class="row q-col-gutter-sm">
          <div class="col-12 col-lg-6">
            <div class="__c">
              <div class="__title">Household Income</div>
              <household-income :person="fullPerson"></household-income>
            </div>
          </div>
          <div class="col-12 col-lg-6">
            <div class="__c">
              <div class="__title">Household Income</div>
              <household-tax
                  toggle
                  :person="fullPerson"
                  :plan="plan"
                  :enrollment="enrollment"
              ></household-tax>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import HouseholdTax from 'components/households/cards/HouseholdTax.vue';
  import HouseholdIncome from 'components/households/cards/HouseholdIncome.vue';
  import {useAtcStore} from 'src/stores/atc-store';

  import {idGet} from 'src/utils/id-get';
  import {computed} from 'vue';
  import {usePpls} from 'stores/ppls';

  const pplsStore = usePpls();

  const props = defineProps({
    enrollment: { required: false },
    plan: { required: false },
    person: { required: true }
  })

  const { item:fullPerson } = idGet({
    store: pplsStore,
    value: computed(() => props.person),
    useAtcStore
  })
</script>

<style lang="scss" scoped>
  .__c {
    margin-top: 30px;
    padding: 4vh 3vw;
    border-radius: 6px;
    background: white;
    box-shadow: 0 2px 7px rgba(0, 0, 0, .1);
    position: relative;
  }

  .__title {
    height: 30px;
    width: 100%;
  }

</style>
