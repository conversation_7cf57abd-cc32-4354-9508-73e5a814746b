#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Find all service schema files
const servicesDir = 'server/src/services';
const services = fs.readdirSync(servicesDir).filter(dir => {
  const schemaFile = path.join(servicesDir, dir, `${dir}.schema.ts`);
  return fs.existsSync(schemaFile);
});

console.log(`Found ${services.length} services to update`);

services.forEach(serviceName => {
  const schemaFile = path.join(servicesDir, serviceName, `${serviceName}.schema.ts`);
  let content = fs.readFileSync(schemaFile, 'utf8');

  // Extract ObjectId fields from the main schema
  const objectIdFields = [];

  // Find the main schema definition - look for the export const pattern
  const schemaRegex = new RegExp(`export const ${serviceName}Schema = Type\\.Object\\(\\{([\\s\\S]*?)\\}, \\{ additionalProperties: false \\}\\)`);
  const schemaMatch = content.match(schemaRegex);

  if (schemaMatch) {
    const schemaBody = schemaMatch[1];

    // Find top-level fields that contain ObjectIdSchema (either directly or nested)
    const lines = schemaBody.split('\n');
    let currentTopLevelField = null;
    let braceDepth = 0;

    for (const line of lines) {
      // Track brace depth to know when we're in nested objects
      const openBraces = (line.match(/\{/g) || []).length;
      const closeBraces = (line.match(/\}/g) || []).length;
      braceDepth += openBraces - closeBraces;

      // If we're at the top level (braceDepth 0) and find a field definition
      if (braceDepth === 0) {
        const fieldMatch = line.match(/^\s*(\w+):/);
        if (fieldMatch) {
          currentTopLevelField = fieldMatch[1];
        }
      }

      // If we find ObjectIdSchema anywhere in this field's definition
      if (line.includes('ObjectIdSchema()') && currentTopLevelField) {
        if (!objectIdFields.includes(currentTopLevelField)) {
          objectIdFields.push(currentTopLevelField);
        }
      }

      // Reset current field when we finish a top-level field definition
      if (braceDepth === 0 && line.includes(',') && !line.includes(':')) {
        currentTopLevelField = null;
      }
    }

    // Always include common ObjectId fields
    if (!objectIdFields.includes('createdBy')) objectIdFields.push('createdBy');
    if (!objectIdFields.includes('updatedBy')) objectIdFields.push('updatedBy');
  }

  console.log(`${serviceName}: Found ObjectId fields: ${objectIdFields.join(', ')}`);

  // Create the new query properties definition
  const queryPropsName = `${serviceName}QueryProperties`;
  const schemaName = `${serviceName}Schema`;

  const newQueryProps = `// Allow querying on any field from the main schema
// Pick only ObjectId fields for type conversion, allow additional properties for flexibility
const ${queryPropsName} = Type.Object({
  ...Type.Pick(${schemaName}, [${objectIdFields.map(f => `'${f}'`).join(', ')}]).properties
}, { additionalProperties: true })`;

  // Find and replace the query properties section more carefully
  // Look for the pattern: const serviceQueryProperties = serviceSchema
  const simplePattern = new RegExp(`const ${queryPropsName} = ${schemaName}$`, 'm');

  if (simplePattern.test(content)) {
    content = content.replace(simplePattern, newQueryProps);
    console.log(`✅ Updated ${serviceName} (simple pattern)`);
  } else {
    // Look for more complex patterns
    const complexPattern = new RegExp(
      `(// .*\\n.*\\n)const ${queryPropsName} = [\\s\\S]*?(?=\\n\\nexport const ${serviceName}QuerySchema)`,
      'g'
    );

    if (complexPattern.test(content)) {
      content = content.replace(complexPattern, newQueryProps + '\n');
      console.log(`✅ Updated ${serviceName} (complex pattern)`);
    } else {
      console.log(`❌ Could not find query properties pattern in ${serviceName}`);
    }
  }

  // Write the updated content back
  fs.writeFileSync(schemaFile, content);
});

console.log('✅ All services updated!');
